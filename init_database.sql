-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS D55DB3dfa120fb0b CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE D55DB3dfa120fb0b;

-- 创建product表
CREATE TABLE IF NOT EXISTS product (
    p_id INT AUTO_INCREMENT PRIMARY KEY,
    p_type VARCHAR(50) NOT NULL COMMENT '商品类型',
    p_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    p_price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    p_quantity INT NOT NULL DEFAULT 0 COMMENT '商品数量',
    p_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '商品时间'
);

-- 清空表数据（如果需要重新初始化）
TRUNCATE TABLE product;

-- 插入20条全新的测试数据（第一页10条，第二页10条）
INSERT INTO product (p_type, p_name, p_price, p_quantity, p_time) VALUES
-- 第一页数据 (ID 1-10)
('智能设备', '小米智能手环7', 299.00, 150, '2024-03-01 09:00:00'),
('智能设备', '华为智能手表GT4', 1899.00, 80, '2024-03-01 10:15:00'),
('智能设备', 'OPPO无线耳机', 599.00, 120, '2024-03-01 11:30:00'),
('智能设备', 'vivo智能音箱', 399.00, 90, '2024-03-01 12:45:00'),
('智能设备', '荣耀平板电脑', 2299.00, 60, '2024-03-01 14:00:00'),
('运动户外', '耐克运动鞋Air Max', 899.00, 200, '2024-03-01 15:15:00'),
('运动户外', '阿迪达斯运动服套装', 699.00, 150, '2024-03-01 16:30:00'),
('运动户外', '李宁篮球鞋', 599.00, 180, '2024-03-01 17:45:00'),
('运动户外', '安踏跑步鞋', 399.00, 220, '2024-03-01 19:00:00'),
('运动户外', '特步休闲鞋', 299.00, 250, '2024-03-01 20:15:00'),

-- 第二页数据 (ID 11-20)
('美妆护肤', '兰蔻粉底液', 459.00, 100, '2024-03-02 09:00:00'),
('美妆护肤', '雅诗兰黛精华液', 899.00, 80, '2024-03-02 10:15:00'),
('美妆护肤', '欧莱雅面膜套装', 199.00, 300, '2024-03-02 11:30:00'),
('美妆护肤', '资生堂防晒霜', 299.00, 200, '2024-03-02 12:45:00'),
('美妆护肤', '娇韵诗护手霜', 159.00, 250, '2024-03-02 14:00:00'),
('厨房电器', '九阳豆浆机', 399.00, 120, '2024-03-02 15:15:00'),
('厨房电器', '美的电压力锅', 599.00, 100, '2024-03-02 16:30:00'),
('厨房电器', '苏泊尔炒锅', 299.00, 150, '2024-03-02 17:45:00'),
('厨房电器', '格兰仕微波炉', 799.00, 80, '2024-03-02 19:00:00'),
('厨房电器', '老板抽油烟机', 2999.00, 50, '2024-03-02 20:15:00');

-- 查看插入的数据
SELECT COUNT(*) as total_records FROM product;
SELECT * FROM product ORDER BY p_id LIMIT 10;
